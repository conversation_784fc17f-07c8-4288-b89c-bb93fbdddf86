#Requires AutoHotkey v2

; 步骤1：检查 AutoHotkey 版本
MsgBox("步骤1：AutoHotkey 版本检查`n版本: " . A_AhkVersion)

; 步骤2：检查 UIA 库文件是否存在
uiaPath := "..\UIA-v2-1.1.0\Lib\UIA.ahk"
if !FileExist(uiaPath) {
    MsgBox("步骤2：UIA 库文件不存在`n路径: " . uiaPath . "`n`n请检查 UIA-v2-1.1.0 文件夹是否在正确位置")
    ExitApp
}

MsgBox("步骤2：UIA 库文件存在，正在加载...")

; 步骤3：加载 UIA 库
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

MsgBox("步骤3：UIA 库加载成功")

; 全局变量
global monitor := {
    active: false,
    coppliaElement: "",
    operationCount: 0,
    logFile: "CoppeliaSim_Diagnostic_Log.txt",
    gui: "",
    listView: ""
}

; 继续诊断步骤
ContinueDiagnosis()

return

; 继续诊断
ContinueDiagnosis() {
    
    ; 步骤3：检查 CoppeliaSim 进程
    if ProcessExist("coppeliasim.exe") {
        MsgBox("步骤3：检测到 CoppeliaSim 进程正在运行")
    } else {
        result := MsgBox("步骤3：未检测到 CoppeliaSim 进程`n是否尝试启动？", "诊断", "YesNo")
        if result = "Yes" {
            TryStartCoppeliaSim()
        }
    }
    
    ; 步骤4：检查窗口
    if WinExist("ahk_exe coppeliasim.exe") {
        MsgBox("步骤4：检测到 CoppeliaSim 窗口")
        windowTitle := WinGetTitle("ahk_exe coppeliasim.exe")
        MsgBox("窗口标题: " . windowTitle)
    } else {
        MsgBox("步骤4：未检测到 CoppeliaSim 窗口")
        ExitApp
    }
    
    ; 步骤5：尝试获取 UIA 元素
    try {
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        MsgBox("步骤5：窗口句柄: " . hwnd)
        
        element := UIA.ElementFromHandle(hwnd)
        MsgBox("步骤5：UIA 元素获取成功")
        
        ; 测试元素属性
        try {
            elementName := element.Name ? element.Name : "[无名称]"
            elementType := element.LocalizedType ? element.LocalizedType : "[无类型]"
            MsgBox("元素信息:`n名称: " . elementName . "`n类型: " . elementType)
        } catch Error as e {
            MsgBox("获取元素属性失败: " . e.Message)
        }
        
        monitor.coppliaElement := element
        
    } catch Error as e {
        MsgBox("步骤5：UIA 元素获取失败`n错误: " . e.Message . "`n错误行: " . e.Line)
        ExitApp
    }
    
    ; 步骤6：创建简单界面
    try {
        CreateSimpleInterface()
        MsgBox("步骤6：界面创建成功`n诊断完成！可以开始使用监控功能。")
    } catch Error as e {
        MsgBox("步骤6：界面创建失败`n错误: " . e.Message)
        ExitApp
    }
}

; 尝试启动 CoppeliaSim
TryStartCoppeliaSim() {
    ; 常见的 CoppeliaSim 安装路径
    paths := [
        "C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe",
        "C:\Program Files (x86)\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe",
        "C:\CoppeliaSimEdu\coppeliasim.exe",
        "D:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe"
    ]
    
    for path in paths {
        if FileExist(path) {
            MsgBox("找到 CoppeliaSim: " . path)
            try {
                Run(path)
                MsgBox("正在启动 CoppeliaSim，请等待...")
                WinWaitActive("ahk_exe coppeliasim.exe", , 30)
                Sleep(3000)
                MsgBox("CoppeliaSim 启动成功")
                return
            } catch Error as e {
                MsgBox("启动失败: " . e.Message)
            }
        }
    }
    
    MsgBox("未找到 CoppeliaSim 安装路径`n请手动启动 CoppeliaSim 后重新运行此脚本")
    ExitApp
}

; 创建简单界面
CreateSimpleInterface() {
    global monitor
    
    monitor.gui := Gui("+Resize", "CoppeliaSim 诊断版监控")
    monitor.gui.SetFont("s10")
    
    ; 状态显示
    monitor.gui.Add("Text", "x10 y10", "诊断状态: 成功")
    monitor.gui.Add("Text", "x10 y35", "操作计数:")
    countText := monitor.gui.Add("Text", "x80 y35 w100", "0")
    countText.Name := "CountText"
    
    ; 控制按钮
    startBtn := monitor.gui.Add("Button", "x10 y60 w100 h30", "开始监控")
    startBtn.OnEvent("Click", (*) => StartSimpleMonitoring())
    
    stopBtn := monitor.gui.Add("Button", "x120 y60 w100 h30", "停止监控")
    stopBtn.OnEvent("Click", (*) => StopSimpleMonitoring())
    
    testBtn := monitor.gui.Add("Button", "x230 y60 w100 h30", "测试点击")
    testBtn.OnEvent("Click", (*) => TestClickDetection())
    
    ; 日志显示
    monitor.gui.Add("Text", "x10 y100", "监控日志:")
    monitor.listView := monitor.gui.Add("ListView", "x10 y120 w500 h200", ["时间", "操作", "详情"])
    
    ; 设置列宽
    monitor.listView.ModifyCol(1, 80)
    monitor.listView.ModifyCol(2, 120)
    monitor.listView.ModifyCol(3, 280)
    
    ; 显示界面
    monitor.gui.Show("w520 h340")
    
    ; 设置事件
    monitor.gui.OnEvent("Close", (*) => ExitApp)
}

; 开始简单监控
StartSimpleMonitoring() {
    global monitor
    
    if !monitor.active {
        monitor.active := true
        SetTimer(SimpleMouseMonitor, 200)
        AddSimpleLog("系统", "监控已开始")
    }
}

; 停止简单监控
StopSimpleMonitoring() {
    global monitor
    
    if monitor.active {
        monitor.active := false
        SetTimer(SimpleMouseMonitor, 0)
        AddSimpleLog("系统", "监控已停止")
    }
}

; 简单鼠标监控
SimpleMouseMonitor() {
    global monitor
    
    if !monitor.active
        return
        
    static lastClickTime := 0
    
    if GetKeyState("LButton", "P") {
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 500) {
            MouseGetPos(&x, &y, &winId)
            
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                try {
                    clickedElement := UIA.ElementFromPoint(x, y)
                    if clickedElement {
                        elementName := clickedElement.Name ? clickedElement.Name : "[无名称]"
                        AddSimpleLog("鼠标点击", elementName . " 位置:(" . x . "," . y . ")")
                    } else {
                        AddSimpleLog("鼠标点击", "未获取到元素 位置:(" . x . "," . y . ")")
                    }
                } catch Error as e {
                    AddSimpleLog("鼠标点击", "获取元素失败: " . e.Message)
                }
            }
            
            lastClickTime := currentTime
        }
    }
}

; 测试点击检测
TestClickDetection() {
    global monitor
    
    try {
        if WinExist("ahk_exe coppeliasim.exe") {
            hwnd := WinExist("ahk_exe coppeliasim.exe")
            element := UIA.ElementFromHandle(hwnd)
            
            ; 尝试查找一些常见元素
            try {
                startBtn := element.FindElement({Name:"Start/resume simulation"})
                AddSimpleLog("测试", "找到开始仿真按钮: " . startBtn.Name)
            } catch {
                AddSimpleLog("测试", "未找到开始仿真按钮")
            }
            
            try {
                buttons := element.FindElements({Type:"Button"})
                AddSimpleLog("测试", "找到 " . buttons.Length . " 个按钮")
            } catch {
                AddSimpleLog("测试", "查找按钮失败")
            }
            
        } else {
            AddSimpleLog("测试", "CoppeliaSim 窗口不存在")
        }
    } catch Error as e {
        AddSimpleLog("测试", "测试失败: " . e.Message)
    }
}

; 添加简单日志
AddSimpleLog(operation, details) {
    global monitor
    
    monitor.operationCount++
    currentTime := FormatTime(A_Now, "HH:mm:ss")
    
    if monitor.listView {
        monitor.listView.Add(, currentTime, operation, details)
        monitor.listView.Modify(monitor.listView.GetCount(), "Vis")
    }
    
    ; 更新计数
    if monitor.gui {
        try {
            countControl := monitor.gui["CountText"]
            countControl.Text := monitor.operationCount
        } catch {
            ; 忽略错误
        }
    }
    
    ; 写入日志文件
    logEntry := currentTime . " | " . operation . " | " . details . "`n"
    FileAppend(logEntry, monitor.logFile, "UTF-8")
}
