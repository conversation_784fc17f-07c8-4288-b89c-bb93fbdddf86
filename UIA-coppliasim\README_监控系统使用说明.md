# CoppeliaSim 操作监控系统使用说明

## 📋 系统概述

本监控系统专门用于实时监控用户在 CoppeliaSim 中的操作行为，并分析操作背后对应的脚本代码和 API 调用。

## 🚀 功能特点

### 1. 实时操作监控
- 监控鼠标点击事件
- 监控键盘操作
- 监控菜单选择
- 监控工具栏操作
- 监控仿真控制操作

### 2. 脚本代码分析
- 自动分析操作对应的 Lua 脚本代码
- 生成可执行的脚本模板
- 记录 API 调用序列
- 提供代码建议和最佳实践

### 3. 数据导出功能
- 生成详细的操作日志
- 导出 Lua 脚本文件
- 生成 API 调用分析报告
- 提供操作统计信息

## 📁 文件说明

### 主要监控程序

1. **`Monitor_coppliasim_simple.ahk`** - 基础实时监控系统
   - 提供图形界面的实时监控
   - 记录所有用户操作
   - 显示操作统计信息

2. **`CoppeliaSim_Script_Monitor.ahk`** - 专业脚本分析器
   - 专注于脚本代码分析
   - 自动生成 Lua 脚本
   - 提供 API 调用序列分析

3. **`Monitor_coppliasim_advanced.ahk`** - 高级监控系统
   - 提供热键控制
   - 支持仿真状态监控
   - 包含完整的控制功能

### 辅助工具

4. **`Debug_Elements.ahk`** - UI 结构分析工具
   - 分析 CoppeliaSim 的 UI 结构
   - 生成元素信息文件
   - 用于调试和开发

## 🔧 使用方法

### 步骤 1：准备环境
1. 确保 CoppeliaSim 已安装并可正常运行
2. 确保 AutoHotkey v2 已安装
3. 确保 UIA-v2 库文件完整

### 步骤 2：选择监控程序
根据需求选择合适的监控程序：

- **基础监控**：运行 `Monitor_coppliasim_simple.ahk`
- **脚本分析**：运行 `CoppeliaSim_Script_Monitor.ahk`
- **高级功能**：运行 `Monitor_coppliasim_advanced.ahk`

### 步骤 3：开始监控
1. 启动选择的监控程序
2. 程序会自动检测或启动 CoppeliaSim
3. 按照提示开始监控（通常是 F9 或 Ctrl+F9）
4. 在 CoppeliaSim 中进行各种操作
5. 观察监控界面的实时反馈

### 步骤 4：查看结果
1. 查看实时监控界面
2. 检查生成的日志文件
3. 导出脚本代码和分析报告

## ⌨️ 热键说明

### 基础监控系统 (Monitor_coppliasim_simple.ahk)
- `F9` - 开始/停止监控
- `F10` - 显示监控统计
- `F11` - 清空监控日志
- `F12` - 退出监控程序

### 脚本分析器 (CoppeliaSim_Script_Monitor.ahk)
- `Ctrl+F9` - 开始/停止监控
- `Ctrl+F10` - 导出脚本代码
- `Ctrl+F11` - 显示API调用序列
- `Ctrl+F12` - 退出程序

### 高级监控系统 (Monitor_coppliasim_advanced.ahk)
- `F1` - 开始仿真
- `F2` - 停止仿真
- `F3` - 检查仿真状态
- `F4` - 显示仿真控件
- `F5` - 退出脚本

## 📊 输出文件说明

### 日志文件
- `CoppeliaSim_Monitor_Log.txt` - 详细操作日志
- `CoppeliaSim_UI_Structure.txt` - UI 结构分析结果

### 脚本文件
- `CoppeliaSim_Generated_Script.lua` - 自动生成的 Lua 脚本
- `CoppeliaSim_Script_Analysis_Report.txt` - 脚本分析报告

## 🎯 监控的操作类型

### 仿真控制
- 开始仿真 → `sim.startSimulation()`
- 停止仿真 → `sim.stopSimulation()`
- 暂停仿真 → `sim.pauseSimulation(true)`

### 视图操作
- 相机平移/旋转 → `sim.setCameraMatrix()`
- 适应视图 → 相机矩阵调整

### 对象操作
- 选择对象 → `sim.getObjectSelection()`
- 移动对象 → `sim.setObjectPosition()`
- 旋转对象 → `sim.setObjectOrientation()`

### 菜单操作
- 文件操作 → `sim.loadScene()`, `sim.saveScene()`
- 添加对象 → `sim.createPrimitiveShape()`
- 插件操作 → `sim.loadPlugin()`

## 🔍 使用示例

### 示例 1：监控仿真操作
1. 运行 `CoppeliaSim_Script_Monitor.ahk`
2. 按 `Ctrl+F9` 开始监控
3. 在 CoppeliaSim 中点击"开始仿真"按钮
4. 系统会检测到操作并生成对应的脚本代码：
   ```lua
   -- 启动仿真
   sim.startSimulation()
   ```

### 示例 2：监控对象操作
1. 在 CoppeliaSim 中选择一个对象
2. 移动该对象
3. 系统会生成相应的脚本：
   ```lua
   -- 获取选中对象
   local selection = sim.getObjectSelection()
   -- 设置对象位置
   sim.setObjectPosition(objectHandle, position)
   ```

## ⚠️ 注意事项

1. **权限要求**：确保 AutoHotkey 有足够的权限访问 CoppeliaSim
2. **性能影响**：监控会占用一定的系统资源，建议在需要时才开启
3. **兼容性**：主要支持 CoppeliaSim Edu 版本
4. **文件路径**：确保 CoppeliaSim 安装路径正确
5. **UIA 库**：确保 UIA-v2 库文件路径正确

## 🛠️ 故障排除

### 常见问题

1. **无法找到 CoppeliaSim 窗口**
   - 检查 CoppeliaSim 是否正在运行
   - 确认进程名为 `coppeliasim.exe`

2. **监控无响应**
   - 重启监控程序
   - 检查 UIA 库是否正确加载

3. **脚本生成不准确**
   - 这是正常现象，生成的脚本需要根据实际需求调整
   - 可以作为参考和起点使用

4. **热键冲突**
   - 修改脚本中的热键定义
   - 选择不冲突的按键组合

## 📈 扩展开发

如需扩展功能，可以：

1. 添加新的操作类型识别
2. 增加更多的 API 映射
3. 改进脚本生成逻辑
4. 添加新的输出格式

## 📞 技术支持

如遇到问题，请检查：
1. AutoHotkey v2 版本是否正确
2. UIA-v2 库是否完整
3. CoppeliaSim 版本兼容性
4. 系统权限设置

---

**版本信息**：v1.0  
**更新日期**：2024年  
**兼容性**：CoppeliaSim Edu, AutoHotkey v2, Windows 10/11
