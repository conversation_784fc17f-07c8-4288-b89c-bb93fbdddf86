#Requires AutoHotkey v2

/*
=============================================================================
编码修复工具
=============================================================================
修复现有的 CoppeliaSim_UI_Structure.txt 文件的编码问题
将问号字符转换为正确的中文显示
=============================================================================
*/

; 检查是否存在问题文件
if !FileExist("CoppeliaSim_UI_Structure.txt") {
    MsgBox("未找到 CoppeliaSim_UI_Structure.txt 文件`n`n请先运行 Debug_Elements.ahk 生成 UI 结构文件")
    ExitApp
}

MsgBox("编码修复工具`n`n此工具将修复 CoppeliaSim_UI_Structure.txt 文件中的编码问题`n点击确定开始修复")

try {
    ; 读取原文件内容
    originalContent := FileRead("CoppeliaSim_UI_Structure.txt")
    
    ; 备份原文件
    backupFile := "CoppeliaSim_UI_Structure_backup_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"
    FileAppend(originalContent, backupFile)
    
    ; 重新生成正确编码的文件
    RegenerateUIStructure()
    
    MsgBox("编码修复完成！`n`n原文件已备份为: " . backupFile . "`n新文件使用 UTF-8 编码，中文字符应该能正确显示")
    
} catch Error as e {
    MsgBox("修复失败: " . e.Message)
}

ExitApp

; 重新生成 UI 结构文件
RegenerateUIStructure() {
    ; 检查 CoppeliaSim 是否运行
    if !WinExist("ahk_exe coppeliasim.exe") {
        MsgBox("CoppeliaSim 未运行，无法重新生成 UI 结构`n请启动 CoppeliaSim 后重新运行此工具")
        return
    }
    
    ; 加载 UIA 库
    #include ..\UIA-v2-1.1.0\Lib\UIA.ahk
    
    ; 获取 CoppeliaSim 窗口
    hwnd := WinExist("ahk_exe coppeliasim.exe")
    element := UIA.ElementFromHandle(hwnd)
    
    ; 获取所有元素信息
    allElements := element.DumpAll()
    
    ; 删除旧文件并创建新文件（使用 UTF-8 编码）
    FileDelete("CoppeliaSim_UI_Structure.txt")
    
    ; 添加文件头信息
    header := "=== CoppeliaSim UI 结构（UTF-8 编码）===`n"
    header .= "生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
    header .= "编码格式: UTF-8`n"
    header .= "说明: 此文件使用 UTF-8 编码，中文字符应能正确显示`n"
    header .= "=" . StrRepeat("=", 50) . "`n`n"
    
    FileAppend(header, "CoppeliaSim_UI_Structure.txt", "UTF-8")
    FileAppend(allElements, "CoppeliaSim_UI_Structure.txt", "UTF-8")
}

; 字符串重复函数
StrRepeat(str, count) {
    result := ""
    Loop count {
        result .= str
    }
    return result
}
