#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
增强监控系统测试脚本
=============================================================================
用于测试改进后的元素识别和脚本分析功能
=============================================================================
*/

; 测试增强的元素信息获取
TestEnhancedElementInfo() {
    ; 检查 CoppeliaSim 是否运行
    if !WinExist("ahk_exe coppeliasim.exe") {
        MsgBox("请先启动 CoppeliaSim")
        return
    }
    
    MsgBox("测试开始`n`n请在 CoppeliaSim 中点击任意元素`n点击后等待3秒查看结果")
    
    ; 等待用户点击
    Sleep(1000)
    
    ; 监控鼠标点击
    Loop 10 {
        if GetKeyState("LButton", "P") {
            ; 等待释放
            while GetKeyState("LButton", "P") {
                Sleep(10)
            }
            
            ; 获取点击位置
            MouseGetPos(&x, &y, &winId)
            
            ; 检查是否在 CoppeliaSim 窗口内
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                try {
                    ; 获取点击位置的元素
                    clickedElement := UIA.ElementFromPoint(x, y)
                    if clickedElement {
                        ; 使用增强的元素信息获取
                        elementInfo := GetEnhancedElementInfo(clickedElement, x, y)
                        
                        ; 分析脚本
                        scriptCode := AnalyzeElementForScriptEnhanced(clickedElement, elementInfo)
                        
                        ; 构建详细信息
                        detailedInfo := BuildEnhancedDetailedInfo(elementInfo, x, y)
                        
                        ; 显示结果
                        result := "=== 增强元素分析结果 ===`n`n"
                        result .= "显示名称: " . elementInfo.DisplayName . "`n"
                        result .= "原始名称: " . elementInfo.Name . "`n"
                        result .= "元素类型: " . elementInfo.Type . "`n"
                        result .= "类名: " . elementInfo.ClassName . "`n"
                        result .= "AutomationId: " . elementInfo.AutomationId . "`n"
                        result .= "帮助文本: " . elementInfo.HelpText . "`n"
                        result .= "父元素: " . elementInfo.ParentName . "`n"
                        result .= "`n脚本建议: " . scriptCode . "`n"
                        result .= "`n详细信息: " . detailedInfo . "`n"
                        
                        ; 显示支持的模式
                        if elementInfo.Patterns.Length > 0 {
                            result .= "`n支持的模式: "
                            for pattern in elementInfo.Patterns {
                                result .= pattern . " "
                            }
                        }
                        
                        MsgBox(result, "元素分析结果")
                        return
                    }
                } catch Error as e {
                    MsgBox("分析失败: " . e.Message)
                    return
                }
            }
        }
        Sleep(100)
    }
    
    MsgBox("未检测到有效的点击操作")
}

; 包含增强函数（从主文件复制）
#include Monitor_coppliasim_simple.ahk

; 运行测试
TestEnhancedElementInfo()
