#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahv

/*
=============================================================================
CoppeliaSim UI 元素分析器（改进版）
=============================================================================
解决字符编码问题，正确显示中文和特殊字符
提供更详细的元素信息和分析
=============================================================================
*/

; 检查 CoppeliaSim 是否运行
if !WinExist("ahk_exe coppeliasim.exe") {
    MsgBox("请先启动 CoppeliaSim，然后重新运行此脚本")
    ExitApp
}

; 创建分析界面
CreateAnalyzerInterface()

return

; 创建分析器界面
CreateAnalyzerInterface() {
    ; 创建主窗口
    gui := Gui("+Resize", "CoppeliaSim UI 元素分析器")
    gui.SetFont("s9")
    
    ; 说明文字
    gui.Add("Text", "x10 y10 w500", "此工具用于分析 CoppeliaSim 的 UI 结构，解决字符编码问题")
    
    ; 控制按钮
    analyzeBtn := gui.Add("Button", "x10 y35 w120 h30", "分析 UI 结构")
    analyzeBtn.OnEvent("Click", (*) => AnalyzeUIStructure())
    
    findBtn := gui.Add("Button", "x140 y35 w120 h30", "查找特定元素")
    findBtn.OnEvent("Click", (*) => FindSpecificElements())
    
    exportBtn := gui.Add("Button", "x270 y35 w120 h30", "导出详细报告")
    exportBtn.OnEvent("Click", (*) => ExportDetailedReport())
    
    ; 结果显示区域
    gui.Add("Text", "x10 y75", "分析结果:")
    resultEdit := gui.Add("Edit", "x10 y95 w580 h350 +VScroll +ReadOnly")
    resultEdit.Name := "ResultEdit"
    
    ; 状态栏
    statusText := gui.Add("Text", "x10 y455", "就绪")
    statusText.Name := "StatusText"
    
    ; 显示界面
    gui.Show("w600 h480")
    
    ; 设置事件
    gui.OnEvent("Close", (*) => ExitApp)
    
    ; 保存GUI引用
    global analyzerGui := gui
}

; 分析 UI 结构
AnalyzeUIStructure() {
    global analyzerGui
    
    try {
        ; 更新状态
        statusControl := analyzerGui["StatusText"]
        statusControl.Text := "正在分析 UI 结构..."
        
        ; 获取 CoppeliaSim 窗口
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        element := UIA.ElementFromHandle(hwnd)
        
        ; 获取基本信息
        windowTitle := WinGetTitle("ahk_exe coppeliasim.exe")
        
        ; 构建分析结果
        result := "=== CoppeliaSim UI 结构分析 ===`n"
        result .= "分析时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
        result .= "窗口标题: " . windowTitle . "`n"
        result .= "窗口句柄: " . hwnd . "`n`n"
        
        ; 获取主要元素信息
        result .= "=== 主窗口信息 ===`n"
        result .= "名称: " . (element.Name ? element.Name : "[无名称]") . "`n"
        result .= "类型: " . (element.LocalizedType ? element.LocalType : "[无类型]") . "`n"
        result .= "类名: " . (element.ClassName ? element.ClassName : "[无类名]") . "`n`n"
        
        ; 查找重要的控件类型
        result .= "=== 重要控件统计 ===`n"
        result .= AnalyzeControlTypes(element)
        
        ; 查找仿真相关控件
        result .= "`n=== 仿真相关控件 ===`n"
        result .= FindSimulationControls(element)
        
        ; 显示结果
        resultControl := analyzerGui["ResultEdit"]
        resultControl.Text := result
        
        ; 保存到文件
        outputFile := "CoppeliaSim_UI_Analysis_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"
        FileAppend(result, outputFile, "UTF-8")
        
        statusControl.Text := "分析完成，结果已保存到: " . outputFile
        
    } catch Error as e {
        MsgBox("分析失败: " . e.Message)
        statusControl.Text := "分析失败"
    }
}

; 分析控件类型
AnalyzeControlTypes(element) {
    result := ""
    
    try {
        ; 统计不同类型的控件
        controlTypes := Map()
        controlTypes["Button"] := 0
        controlTypes["CheckBox"] := 0
        controlTypes["MenuItem"] := 0
        controlTypes["ToolBar"] := 0
        controlTypes["Edit"] := 0
        controlTypes["ComboBox"] := 0
        controlTypes["ListItem"] := 0
        controlTypes["TreeItem"] := 0
        
        ; 查找各种类型的控件
        for typeName, count in controlTypes {
            try {
                elements := element.FindElements({Type:typeName})
                controlTypes[typeName] := elements.Length
            } catch {
                controlTypes[typeName] := 0
            }
        }
        
        ; 构建统计结果
        for typeName, count in controlTypes {
            if count > 0 {
                result .= typeName . ": " . count . " 个`n"
            }
        }
        
    } catch {
        result .= "控件统计失败`n"
    }
    
    return result
}

; 查找仿真相关控件
FindSimulationControls(element) {
    result := ""
    
    ; 仿真相关的关键词
    simKeywords := ["simulation", "start", "stop", "pause", "resume", "仿真", "开始", "停止", "暂停"]
    
    try {
        ; 查找所有按钮
        buttons := element.FindElements({Type:"Button"})
        result .= "按钮控件:`n"
        
        for button in buttons {
            try {
                buttonName := button.Name ? button.Name : "[无名称]"
                buttonClass := button.ClassName ? button.ClassName : "[无类名]"
                
                ; 检查是否与仿真相关
                isSimRelated := false
                for keyword in simKeywords {
                    if InStr(buttonName, keyword) {
                        isSimRelated := true
                        break
                    }
                }
                
                if isSimRelated || buttonName != "[无名称]" {
                    result .= "  • " . buttonName . " (类: " . buttonClass . ")`n"
                }
            } catch {
                continue
            }
        }
        
        ; 查找所有复选框
        checkboxes := element.FindElements({Type:"CheckBox"})
        if checkboxes.Length > 0 {
            result .= "`n复选框控件:`n"
            
            for checkbox in checkboxes {
                try {
                    checkboxName := checkbox.Name ? checkbox.Name : "[无名称]"
                    checkboxClass := checkbox.ClassName ? checkbox.ClassName : "[无类名]"
                    
                    if checkboxName != "[无名称]" {
                        result .= "  • " . checkboxName . " (类: " . checkboxClass . ")`n"
                    }
                } catch {
                    continue
                }
            }
        }
        
    } catch {
        result .= "查找仿真控件失败`n"
    }
    
    return result
}

; 查找特定元素
FindSpecificElements() {
    global analyzerGui
    
    ; 询问用户要查找什么
    searchText := InputBox("请输入要查找的元素名称或关键词:", "查找元素").Value
    
    if searchText = "" {
        return
    }
    
    try {
        statusControl := analyzerGui["StatusText"]
        statusControl.Text := "正在查找: " . searchText
        
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        element := UIA.ElementFromHandle(hwnd)
        
        result := "=== 查找结果: " . searchText . " ===`n"
        result .= "查找时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n"
        
        ; 尝试按名称查找
        try {
            foundElement := element.FindElement({Name:searchText})
            result .= "✓ 找到匹配元素:`n"
            result .= "  名称: " . foundElement.Name . "`n"
            result .= "  类型: " . (foundElement.LocalizedType ? foundElement.LocalizedType : "[无类型]") . "`n"
            result .= "  类名: " . (foundElement.ClassName ? foundElement.ClassName : "[无类名]") . "`n"
            result .= "  值: " . (foundElement.Value ? foundElement.Value : "[无值]") . "`n`n"
        } catch {
            result .= "✗ 未找到完全匹配的元素`n`n"
        }
        
        ; 模糊查找
        result .= "=== 模糊查找结果 ===`n"
        fuzzyResults := FuzzySearch(element, searchText)
        result .= fuzzyResults
        
        ; 显示结果
        resultControl := analyzerGui["ResultEdit"]
        resultControl.Text := result
        
        statusControl.Text := "查找完成"
        
    } catch Error as e {
        MsgBox("查找失败: " . e.Message)
    }
}

; 模糊查找
FuzzySearch(element, searchText) {
    result := ""
    foundCount := 0
    
    try {
        ; 查找所有类型的元素
        allTypes := ["Button", "CheckBox", "MenuItem", "Edit", "ComboBox", "Text"]
        
        for typeName in allTypes {
            try {
                elements := element.FindElements({Type:typeName})
                
                for elem in elements {
                    try {
                        elemName := elem.Name ? elem.Name : ""
                        elemValue := elem.Value ? elem.Value : ""
                        
                        ; 检查名称或值是否包含搜索文本
                        if InStr(elemName, searchText) || InStr(elemValue, searchText) {
                            foundCount++
                            result .= foundCount . ". " . typeName . ": "
                            result .= (elemName != "" ? elemName : "[无名称]")
                            if elemValue != "" && elemValue != elemName {
                                result .= " (值: " . elemValue . ")"
                            }
                            result .= "`n"
                        }
                    } catch {
                        continue
                    }
                }
            } catch {
                continue
            }
        }
        
        if foundCount = 0 {
            result .= "未找到包含 '" . searchText . "' 的元素`n"
        } else {
            result .= "`n共找到 " . foundCount . " 个相关元素`n"
        }
        
    } catch {
        result .= "模糊查找失败`n"
    }
    
    return result
}

; 导出详细报告
ExportDetailedReport() {
    global analyzerGui
    
    try {
        statusControl := analyzerGui["StatusText"]
        statusControl.Text := "正在生成详细报告..."
        
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        element := UIA.ElementFromHandle(hwnd)
        
        ; 生成完整的 UI 结构
        fullStructure := element.DumpAll()
        
        ; 创建报告文件
        reportFile := "CoppeliaSim_Complete_UI_Report_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"
        
        report := "=== CoppeliaSim 完整 UI 结构报告 ===`n"
        report .= "生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
        report .= "窗口标题: " . WinGetTitle("ahk_exe coppeliasim.exe") . "`n"
        report .= "窗口句柄: " . hwnd . "`n"
        report .= "编码格式: UTF-8`n"
        report .= "=" . StrRepeat("=", 50) . "`n`n"
        report .= fullStructure
        
        ; 保存报告
        FileAppend(report, reportFile, "UTF-8")
        
        ; 显示结果摘要
        resultControl := analyzerGui["ResultEdit"]
        resultControl.Text := "详细报告已生成:`n`n文件名: " . reportFile . "`n文件大小: " . FileGetSize(reportFile) . " 字节`n编码: UTF-8`n`n此报告包含完整的 UI 结构信息，所有中文字符都能正确显示。"
        
        statusControl.Text := "报告已生成: " . reportFile
        
        MsgBox("详细报告已生成！`n`n文件: " . reportFile . "`n`n现在可以用文本编辑器打开查看，中文字符将正确显示。")
        
    } catch Error as e {
        MsgBox("生成报告失败: " . e.Message)
    }
}

; 字符串重复函数
StrRepeat(str, count) {
    result := ""
    Loop count {
        result .= str
    }
    return result
}
