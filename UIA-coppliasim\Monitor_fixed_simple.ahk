#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
CoppeliaSim 修复版监控系统
=============================================================================
修复了语法错误和编码问题的稳定版本

功能：
1. 实时监控用户在 CoppeliaSim 中的操作
2. 正确的UTF-8编码日志
3. 优化的监控逻辑，减少无意义的记录
4. 支持窗口大小调整

热键：
F9  - 开始/停止监控
F10 - 显示统计
F11 - 清空日志
F12 - 退出程序
=============================================================================
*/

; 全局变量
global monitor := {
    active: false,
    coppliaElement: "",
    operationCount: 0,
    logFile: "CoppeliaSim_Fixed_Monitor_Log.txt",
    gui: "",
    listView: ""
}

; 初始化
InitializeFixedMonitor()

; 设置热键
F9::ToggleMonitoring()
F10::ShowStatistics()
F11::ClearLog()
F12::ExitMonitor()

; 显示主界面
ShowMonitorInterface()

return

; 初始化监控系统
InitializeFixedMonitor() {
    global monitor
    
    ; 检查 CoppeliaSim
    if !WinExist("ahk_exe coppeliasim.exe") {
        result := MsgBox("CoppeliaSim 未运行，是否启动？", "修复版监控系统", "YesNo")
        if result = "Yes" {
            Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
            WinWaitActive("ahk_exe coppeliasim.exe", , 30)
            Sleep(3000)
        } else {
            ExitApp
        }
    }
    
    try {
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        monitor.coppliaElement := UIA.ElementFromHandle(hwnd)
        
        ; 初始化日志文件 - 使用 UTF-8 编码
        FileDelete(monitor.logFile)
        FileAppend("=== CoppeliaSim 修复版监控日志 ===`n", monitor.logFile, "UTF-8")
        FileAppend("监控开始时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n", monitor.logFile, "UTF-8")
        
        AddLog("修复版监控系统初始化成功")
        
    } catch Error as e {
        MsgBox("初始化失败: " . e.Message)
        ExitApp
    }
}

; 显示监控界面
ShowMonitorInterface() {
    global monitor
    
    ; 创建主界面
    monitor.gui := Gui("+Resize +MinSize640x480", "CoppeliaSim 修复版监控系统")
    monitor.gui.SetFont("s9")
    
    ; 状态显示
    monitor.gui.Add("Text", "x10 y10", "监控状态:")
    statusText := monitor.gui.Add("Text", "x80 y10 w100 c0xFF0000", "未开始")
    statusText.Name := "StatusText"
    
    monitor.gui.Add("Text", "x10 y35", "操作计数:")
    countText := monitor.gui.Add("Text", "x80 y35 w100", "0")
    countText.Name := "CountText"
    
    ; 控制按钮
    startBtn := monitor.gui.Add("Button", "x200 y10 w80 h25", "开始监控")
    startBtn.OnEvent("Click", (*) => ToggleMonitoring())
    
    clearBtn := monitor.gui.Add("Button", "x290 y10 w80 h25", "清空日志")
    clearBtn.OnEvent("Click", (*) => ClearLog())
    
    statsBtn := monitor.gui.Add("Button", "x380 y10 w80 h25", "显示统计")
    statsBtn.OnEvent("Click", (*) => ShowStatistics())
    
    ; 日志显示
    monitor.gui.Add("Text", "x10 y60", "操作日志:")
    monitor.listView := monitor.gui.Add("ListView", "x10 y80 w700 h350 +Grid", ["时间", "操作类型", "元素名称", "脚本代码", "详细描述"])
    
    ; 设置列宽
    monitor.listView.ModifyCol(1, 70)   ; 时间
    monitor.listView.ModifyCol(2, 90)   ; 操作类型
    monitor.listView.ModifyCol(3, 140)  ; 元素名称
    monitor.listView.ModifyCol(4, 200)  ; 脚本代码
    monitor.listView.ModifyCol(5, 180)  ; 详细描述
    
    ; 状态栏
    statusBar := monitor.gui.Add("Text", "x10 y440", "热键: F9-开始/停止 | F10-统计 | F11-清空 | F12-退出")
    statusBar.Name := "StatusBar"
    
    ; 显示界面
    monitor.gui.Show("w720 h470")
    
    ; 设置事件
    monitor.gui.OnEvent("Close", (*) => ExitMonitor())
    monitor.gui.OnEvent("Size", OnGuiResize)
}

; 窗口大小调整
OnGuiResize(GuiObj, MinMax, Width, Height) {
    global monitor
    
    if MinMax = -1
        return
        
    if monitor.listView {
        newWidth := Width - 30
        newHeight := Height - 130
        
        if newWidth < 400
            newWidth := 400
        if newHeight < 200
            newHeight := 200
            
        monitor.listView.Move(, , newWidth, newHeight)
        
        try {
            statusBarControl := GuiObj["StatusBar"]
            statusBarControl.Move(10, Height - 30)
        } catch {
            ; 忽略错误
        }
    }
}

; 开始/停止监控
ToggleMonitoring() {
    global monitor
    
    if !monitor.active {
        try {
            StartFixedMonitoring()
            monitor.active := true
            UpdateStatus("监控中", 0x00FF00)
            AddLog("开始修复版监控")
        } catch Error as e {
            MsgBox("启动监控失败: " . e.Message)
        }
    } else {
        StopFixedMonitoring()
        monitor.active := false
        UpdateStatus("已停止", 0xFF0000)
        AddLog("停止监控")
    }
}

; 开始监控
StartFixedMonitoring() {
    SetTimer(MonitorMouseOperations, 100)
    SetTimer(MonitorKeyboardOperations, 200)
    SetTimer(MonitorSimulationStatus, 1000)
}

; 停止监控
StopFixedMonitoring() {
    SetTimer(MonitorMouseOperations, 0)
    SetTimer(MonitorKeyboardOperations, 0)
    SetTimer(MonitorSimulationStatus, 0)
}

; 监控鼠标操作
MonitorMouseOperations() {
    global monitor
    
    if !monitor.active
        return
        
    static lastClickTime := 0
    static wasPressed := false
    
    isPressed := GetKeyState("LButton", "P")
    
    if !wasPressed && isPressed {
        wasPressed := true
    } else if wasPressed && !isPressed {
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 500) {
            MouseGetPos(&x, &y, &winId)
            
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                try {
                    clickedElement := UIA.ElementFromPoint(x, y)
                    if clickedElement {
                        elementName := clickedElement.Name ? clickedElement.Name : "[无名称]"
                        elementType := clickedElement.LocalizedType ? clickedElement.LocalizedType : "未知类型"
                        
                        if ShouldRecordElement(elementName, elementType) {
                            scriptCode := AnalyzeElementScript(elementName, elementType)
                            detailedInfo := "类型:" . elementType . " | 位置:(" . x . "," . y . ")"
                            
                            LogDetailedOperation("鼠标点击", elementName, scriptCode, detailedInfo)
                        }
                    }
                } catch {
                    ; 忽略错误
                }
            }
            
            lastClickTime := currentTime
        }
        wasPressed := false
    }
}

; 监控键盘操作
MonitorKeyboardOperations() {
    global monitor
    
    if !monitor.active
        return
        
    static lastKeyTime := 0
    static importantKeys := ["Enter", "Space", "Delete", "Escape"]
    
    for key in importantKeys {
        if GetKeyState(key, "P") {
            currentTime := A_TickCount
            if (currentTime - lastKeyTime > 500) {
                if WinActive("ahk_exe coppeliasim.exe") {
                    scriptCode := AnalyzeKeyScript(key)
                    detailedInfo := "在 CoppeliaSim 中按下 " . key . " 键"
                    LogDetailedOperation("按键操作", key, scriptCode, detailedInfo)
                    lastKeyTime := currentTime
                }
            }
        }
    }
}

; 监控仿真状态
MonitorSimulationStatus() {
    global monitor
    
    if !monitor.active
        return
        
    static lastSimulationState := ""
    
    try {
        if WinExist("ahk_exe coppeliasim.exe") {
            windowTitle := WinGetTitle("ahk_exe coppeliasim.exe")
            
            if InStr(windowTitle, "SIMULATION STOPPED") {
                if lastSimulationState != "STOPPED" {
                    LogDetailedOperation("状态变化", "仿真停止", "sim.stopSimulation()", "仿真状态变为停止")
                    lastSimulationState := "STOPPED"
                }
            } else if InStr(windowTitle, "fps") {
                if lastSimulationState != "RUNNING" {
                    LogDetailedOperation("状态变化", "仿真运行", "sim.startSimulation()", "仿真状态变为运行")
                    lastSimulationState := "RUNNING"
                }
            }
        }
    } catch {
        ; 忽略错误
    }
}

; 判断是否记录元素
ShouldRecordElement(elementName, elementType) {
    if elementName = "[无名称]" && elementType = "未知类型"
        return false
    
    meaningfulTypes := ["按钮", "Button", "复选框", "CheckBox", "菜单项", "MenuItem"]
    
    for type in meaningfulTypes {
        if InStr(elementType, type)
            return true
    }
    
    if elementName != "[无名称]" && elementName != ""
        return true
    
    return false
}

; 分析元素脚本
AnalyzeElementScript(elementName, elementType) {
    if InStr(elementName, "Start") && InStr(elementName, "simulation") {
        return "sim.startSimulation()"
    } else if InStr(elementName, "Stop") && InStr(elementName, "simulation") {
        return "sim.stopSimulation()"
    } else if InStr(elementName, "Camera") {
        return "sim.setCameraMatrix()"
    } else {
        return "-- " . elementType . "操作"
    }
}

; 分析按键脚本
AnalyzeKeyScript(key) {
    switch key {
        case "Enter":
            return "-- 确认操作"
        case "Space":
            return "-- 空格键操作"
        case "Delete":
            return "sim.removeObject()"
        case "Escape":
            return "-- 取消操作"
        default:
            return "-- 按键: " . key
    }
}

; 记录详细操作
LogDetailedOperation(operationType, elementName, scriptCode, detailedInfo) {
    global monitor
    
    monitor.operationCount++
    currentTime := FormatTime(A_Now, "HH:mm:ss")
    
    if monitor.listView {
        monitor.listView.Add(, currentTime, operationType, elementName, scriptCode, detailedInfo)
        monitor.listView.Modify(monitor.listView.GetCount(), "Vis")
    }
    
    logEntry := "时间: " . currentTime . "`n"
    logEntry .= "操作: " . operationType . "`n"
    logEntry .= "元素: " . elementName . "`n"
    logEntry .= "脚本: " . scriptCode . "`n"
    logEntry .= "详情: " . detailedInfo . "`n"
    logEntry .= "---`n"
    
    FileAppend(logEntry, monitor.logFile, "UTF-8")
    
    UpdateOperationCount()
}

; 添加系统日志
AddLog(message) {
    LogDetailedOperation("系统", "监控系统", "-- 系统操作", message)
}

; 更新状态显示
UpdateStatus(status, color) {
    global monitor
    
    if monitor.gui {
        try {
            statusControl := monitor.gui["StatusText"]
            statusControl.Text := status
            statusControl.Opt("c" . Format("0x{:06X}", color))
        } catch {
            ; 忽略错误
        }
    }
}

; 更新操作计数
UpdateOperationCount() {
    global monitor
    
    if monitor.gui {
        try {
            countControl := monitor.gui["CountText"]
            countControl.Text := monitor.operationCount
        } catch {
            ; 忽略错误
        }
    }
}

; 显示统计信息
ShowStatistics() {
    global monitor
    
    if monitor.operationCount = 0 {
        MsgBox("暂无监控数据")
        return
    }
    
    statsText := "=== 监控统计信息 ===`n`n"
    statsText .= "总操作次数: " . monitor.operationCount . "`n"
    statsText .= "日志文件: " . monitor.logFile . "`n`n"
    statsText .= "监控功能:`n"
    statsText .= "• 鼠标点击操作`n"
    statsText .= "• 键盘按键操作`n"
    statsText .= "• 仿真状态变化`n"
    statsText .= "• UTF-8编码日志`n"
    
    MsgBox(statsText, "监控统计")
}

; 清空日志
ClearLog() {
    global monitor
    
    result := MsgBox("确定要清空所有监控日志吗？", "确认清空", "YesNo")
    if result = "Yes" {
        if monitor.listView {
            monitor.listView.Delete()
        }
        
        monitor.operationCount := 0
        UpdateOperationCount()
        
        try {
            FileDelete(monitor.logFile)
            FileAppend("=== CoppeliaSim 修复版监控日志 ===`n", monitor.logFile, "UTF-8")
            FileAppend("日志清空时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n", monitor.logFile, "UTF-8")
        } catch {
            ; 忽略错误
        }
        
        AddLog("监控日志已清空")
    }
}

; 退出监控
ExitMonitor() {
    global monitor
    
    if monitor.active {
        StopFixedMonitoring()
    }
    
    AddLog("监控系统退出")
    ExitApp
}
