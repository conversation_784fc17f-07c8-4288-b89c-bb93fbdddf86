=== CoppeliaSim ��ϸ�������� ===
����ʱ��: 2025-07-31 08:59:27
�ܲ�������: 14

=== ��ϸ������¼ ===
=== CoppeliaSim ���������־ ===
��ؿ�ʼʱ��: 2025-07-31 08:57:46

ʱ��: 08:57:46
����: ϵͳ
Ԫ��: ���ϵͳ
�ű�: -- ������¼
����: ���ϵͳ��ʼ���ɹ�
---
ʱ��: 08:57:48
����: ϵͳ
Ԫ��: ���ϵͳ
�ű�: -- ������¼
����: �¼����������
---
ʱ��: 08:57:48
����: ϵͳ
Ԫ��: ���ϵͳ
�ű�: -- ������¼
����: ��ʼ��� CoppeliaSim ����
---
ʱ��: 08:58:00
����: ״̬�仯
Ԫ��: ���濪ʼ
�ű�: sim.startSimulation()
����: ����״̬��ֹͣ��Ϊ����
---
ʱ��: 08:58:24
����: ״̬�仯
Ԫ��: ����ֹͣ
�ű�: sim.stopSimulation()
����: ����״̬�����б�Ϊֹͣ
---
ʱ��: 08:58:30
����: �����
Ԫ��: �鿴
�ű�: -- �ɵ��ò���: �鿴
����: ����:�˵���Ŀ | ģʽ:Invoke,ScrollItem,ExpandCollapse | λ��:(254,27)
---
ʱ��: 08:58:32
����: ״̬�仯
Ԫ��: ���濪ʼ
�ű�: sim.startSimulation()
����: ����״̬��ֹͣ��Ϊ����
---
ʱ��: 08:58:49
����: �����
Ԫ��: �༭
�ű�: -- �ɵ��ò���: �༭
����: ����:�˵���Ŀ | ģʽ:Invoke,ScrollItem,ExpandCollapse | λ��:(115,18)
---
ʱ��: 08:58:57
����: �����
Ԫ��: ѡ��
�ű�: -- �ɵ��ò���: ѡ��
����: ����:�˵���Ŀ | ģʽ:Invoke,ScrollItem,ExpandCollapse | λ��:(192,29)
---
ʱ��: 08:58:58
����: �����
Ԫ��: �鿴
�ű�: -- �ɵ��ò���: �鿴
����: ����:�˵���Ŀ | ģʽ:Invoke,ScrollItem,ExpandCollapse | λ��:(258,25)
---
ʱ��: 08:59:00
����: �����
Ԫ��: ����
�ű�: -- �ɵ��ò���: ����
����: ����:�˵���Ŀ | ģʽ:Invoke,ScrollItem,ExpandCollapse | λ��:(372,25)
---
ʱ��: 08:59:03
����: �����
Ԫ��: �ն�
�ű�: -- �ɵ��ò���: �ն�
����: ����:�˵���Ŀ | ģʽ:Invoke,ScrollItem,ExpandCollapse | λ��:(417,22)
---
ʱ��: 08:59:08
����: ״̬�仯
Ԫ��: ����ֹͣ
�ű�: sim.stopSimulation()
����: ����״̬�����б�Ϊֹͣ
---
ʱ��: 08:59:12
����: �����
Ԫ��: Monitor_coppliasim_simple.ahk
�ű�: -- ����: Monitor_coppliasim_simple.ahk
����: ����:ѡ���Ŀ | ģʽ:SelectionItem,ScrollItem | λ��:(475,38)
---
