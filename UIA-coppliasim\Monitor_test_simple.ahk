#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
CoppeliaSim 简单测试监控器
=============================================================================
这是一个最简化的测试版本，用于验证基本功能是否正常工作
避免复杂的事件处理，只使用最基本的监控方法

功能：
1. 检测 CoppeliaSim 窗口
2. 监控鼠标点击
3. 简单的操作记录
4. 基本的脚本建议

使用方法：
1. 确保 CoppeliaSim 正在运行
2. 运行此脚本
3. 按 F9 开始监控
4. 在 CoppeliaSim 中进行操作
5. 查看控制台输出

热键：
F9 - 开始/停止监控
F12 - 退出
=============================================================================
*/

; 全局变量
global isMonitoring := false
global operationCount := 0
global logFile := "CoppeliaSim_Test_Log.txt"

; 初始化
MsgBox("CoppeliaSim 简单测试监控器`n`n" .
       "热键说明：`n" .
       "F9 - 开始/停止监控`n" .
       "F12 - 退出程序`n`n" .
       "请确保 CoppeliaSim 正在运行，然后按 F9 开始监控。")

; 设置热键
F9::ToggleMonitoring()
F12::ExitApp

; 检查 CoppeliaSim
CheckCoppeliaSim()

return

; 检查 CoppeliaSim 是否运行
CheckCoppeliaSim() {
    if !WinExist("ahk_exe coppeliasim.exe") {
        result := MsgBox("未检测到 CoppeliaSim，是否启动？", "检查程序", "YesNo")
        if result = "Yes" {
            try {
                Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
                WinWaitActive("ahk_exe coppeliasim.exe", , 30)
                Sleep(2000)
                MsgBox("CoppeliaSim 已启动，请按 F9 开始监控。")
            } catch {
                MsgBox("启动 CoppeliaSim 失败，请手动启动后再运行监控。")
            }
        }
    } else {
        MsgBox("检测到 CoppeliaSim 正在运行，可以开始监控。")
    }
}

; 开始/停止监控
ToggleMonitoring() {
    global isMonitoring
    
    if !isMonitoring {
        ; 开始监控
        if !WinExist("ahk_exe coppeliasim.exe") {
            MsgBox("CoppeliaSim 未运行，无法开始监控。")
            return
        }
        
        isMonitoring := true
        SetTimer(MonitorOperations, 200)
        
        ; 初始化日志文件
        FileDelete(logFile)
        FileAppend("=== CoppeliaSim 测试监控日志 ===`n", logFile)
        FileAppend("监控开始时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n", logFile)
        
        ToolTip("监控已开始 - 在 CoppeliaSim 中进行操作", 10, 10)
        SetTimer(() => ToolTip(), -3000)
        
    } else {
        ; 停止监控
        isMonitoring := false
        SetTimer(MonitorOperations, 0)
        
        ToolTip("监控已停止 - 共记录 " . operationCount . " 个操作", 10, 10)
        SetTimer(() => ToolTip(), -3000)
        
        ; 显示结果
        if operationCount > 0 {
            MsgBox("监控完成！`n`n" .
                   "记录的操作数量: " . operationCount . "`n" .
                   "日志文件: " . logFile . "`n`n" .
                   "请查看日志文件了解详细信息。")
        }
    }
}

; 监控操作
MonitorOperations() {
    global isMonitoring, operationCount
    
    if !isMonitoring
        return
        
    static lastClickTime := 0
    static lastTitle := ""
    
    ; 监控鼠标点击
    if GetKeyState("LButton", "P") {
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 500) { ; 防止重复检测
            MouseGetPos(&x, &y, &winId)
            
            ; 检查是否在 CoppeliaSim 窗口内
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                DetectClickOperation(x, y)
                lastClickTime := currentTime
            }
        }
    }
    
    ; 监控窗口标题变化（仿真状态）
    try {
        if WinExist("ahk_exe coppeliasim.exe") {
            currentTitle := WinGetTitle("ahk_exe coppeliasim.exe")
            if currentTitle != lastTitle && lastTitle != "" {
                DetectStatusChange(lastTitle, currentTitle)
            }
            lastTitle := currentTitle
        }
    } catch {
        ; 忽略错误
    }
}

; 检测点击操作
DetectClickOperation(x, y) {
    try {
        ; 尝试获取点击位置的元素信息
        clickedElement := UIA.ElementFromPoint(x, y)
        if clickedElement {
            elementName := clickedElement.Name ? clickedElement.Name : "[无名称]"
            elementType := clickedElement.LocalizedType ? clickedElement.LocalizedType : "未知类型"
            
            ; 分析操作类型
            scriptSuggestion := AnalyzeOperation(elementName, elementType)
            
            ; 记录操作
            RecordOperation("鼠标点击", elementName, elementType, scriptSuggestion, x, y)
            
            ; 显示实时提示
            ToolTip("检测到点击: " . elementName, x + 10, y + 10)
            SetTimer(() => ToolTip(), -1500)
        }
    } catch {
        ; 如果获取元素失败，记录基本信息
        RecordOperation("鼠标点击", "未知元素", "未知", "-- 点击操作", x, y)
    }
}

; 检测状态变化
DetectStatusChange(oldTitle, newTitle) {
    scriptSuggestion := ""
    
    if InStr(newTitle, "SIMULATION STOPPED") && !InStr(oldTitle, "SIMULATION STOPPED") {
        scriptSuggestion := "sim.stopSimulation()"
        RecordOperation("状态变化", "仿真停止", "状态", scriptSuggestion)
    } else if !InStr(newTitle, "SIMULATION STOPPED") && InStr(oldTitle, "SIMULATION STOPPED") {
        scriptSuggestion := "sim.startSimulation()"
        RecordOperation("状态变化", "仿真开始", "状态", scriptSuggestion)
    }
}

; 分析操作
AnalyzeOperation(elementName, elementType) {
    ; 根据元素名称分析对应的脚本
    if InStr(elementName, "Start") && InStr(elementName, "simulation") {
        return "sim.startSimulation()"
    } else if InStr(elementName, "Stop") && InStr(elementName, "simulation") {
        return "sim.stopSimulation()"
    } else if InStr(elementName, "Suspend") && InStr(elementName, "simulation") {
        return "sim.pauseSimulation(true)"
    } else if InStr(elementName, "Camera") {
        if InStr(elementName, "pan") {
            return "-- 相机平移操作"
        } else if InStr(elementName, "rotate") {
            return "-- 相机旋转操作"
        } else {
            return "sim.setCameraMatrix(cameraHandle, matrix)"
        }
    } else if InStr(elementName, "selection") || InStr(elementName, "Selection") {
        return "sim.getObjectSelection()"
    } else if InStr(elementName, "File") {
        return "sim.loadScene() / sim.saveScene()"
    } else if InStr(elementName, "Add") {
        return "sim.createPrimitiveShape(sim.primitiveshape_cuboid, {1,1,1})"
    } else if InStr(elementName, "Plugins") {
        return "sim.loadPlugin('pluginName')"
    } else if InStr(elementName, "Undo") {
        return "-- 撤销操作"
    } else if InStr(elementName, "Redo") {
        return "-- 重做操作"
    } else if elementType = "按钮" || elementType = "Button" {
        return "-- 按钮操作: " . elementName
    } else if elementType = "菜单项" || elementType = "MenuItem" {
        return "-- 菜单操作: " . elementName
    } else {
        return "-- " . elementType . "操作"
    }
}

; 记录操作
RecordOperation(operationType, elementName, elementType, scriptSuggestion, x := 0, y := 0) {
    global operationCount, logFile
    
    operationCount++
    currentTime := FormatTime(A_Now, "HH:mm:ss")
    
    ; 构建日志条目
    logEntry := "时间: " . currentTime . "`n"
    logEntry .= "操作: " . operationType . "`n"
    logEntry .= "元素: " . elementName . "`n"
    logEntry .= "类型: " . elementType . "`n"
    logEntry .= "脚本: " . scriptSuggestion . "`n"
    
    if x > 0 && y > 0 {
        logEntry .= "位置: (" . x . ", " . y . ")`n"
    }
    
    logEntry .= "---`n`n"
    
    ; 写入日志文件
    FileAppend(logEntry, logFile)
    
    ; 控制台输出（可选）
    ; OutputDebug(operationType . ": " . elementName . " -> " . scriptSuggestion)
}
